import { IClusterModelExtension } from '@shell/core/types-provisioning';

/**
 * Model extension for management.cattle.io.cluster to fix EKS provider logo issue
 */
export default class EKSClusterModelExtension implements IClusterModelExtension {
  /**
   * Use this extension for EKS clusters
   */
  useFor(cluster) {
    // Check if this is an EKS cluster by looking at the provider
    return cluster?.status?.provider === 'eks' || 
           cluster?.status?.provider?.startsWith('eks.');
  }

  /**
   * Override the provider logo for EKS clusters
   */
  providerLogo(cluster) {
    try {
      // Use the amazoneks logo from our assets
      return require('../assets/amazoneks.svg');
    } catch (e) {
      console.warn('Could not load EKS provider logo from rancher-saas assets, falling back to shell assets');
      try {
        // Fallback to shell assets
        return require('~shell/assets/images/providers/amazoneks.svg');
      } catch (e2) {
        console.warn('Could not load EKS provider logo from shell assets, using kubernetes fallback');
        // Final fallback to kubernetes logo
        return require('~shell/assets/images/providers/kubernetes.svg');
      }
    }
  }

  /**
   * Override the provider menu logo for EKS clusters
   */
  providerMenuLogo(cluster) {
    return this.providerLogo(cluster);
  }

  /**
   * Override the provider nav logo for EKS clusters
   */
  providerNavLogo(cluster) {
    return this.providerLogo(cluster);
  }
}
